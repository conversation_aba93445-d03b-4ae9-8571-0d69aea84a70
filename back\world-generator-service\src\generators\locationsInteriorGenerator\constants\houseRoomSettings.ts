import { DecorationZoneType, LocationSubtype, MaterialTexture, TerrainType } from '../../../shared/enums';

export interface HouseRoomSettings {
  type: string;
  xyMin: number;
  xyMax: number;
  door: number;
  smallerRooms: number;
  size: 1 | 2 | 3; // 1 - маленький, 2 - средний, 3 - большой
  room: boolean; // является ли комнатой (может быть внутри здания)
  building: boolean; // является ли зданием (может быть отдельно стоящим)
  decorationZoneType: DecorationZoneType;
  canBeUsed: LocationSubtype[] | 'all'; // типы локаций где может использоваться
  required: boolean; // обязательное здание для данного типа локации
  floorMaterial: TerrainType; // материал пола для данного типа здания/комнаты
  roofMaterial: MaterialTexture; // материал крыши для данного типа здания/комнаты
}

export const houseRoomSettings: HouseRoomSettings[] = [
  // Медицинские помещения
  {
    type: 'firstAid',
    xyMin: 7,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.HOSPITAL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.LABORATORY, LocationSubtype.MILITARY, LocationSubtype.FACTORY],
    required: false,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },
  {
    type: 'hospital',
    xyMin: 22,
    xyMax: 30,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.HOSPITAL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.HOSPITAL],
    required: true,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },

  // Санитарные помещения
  {
    type: 'toilet',
    xyMin: 4,
    xyMax: 6,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.BATHROOM,
    canBeUsed: 'all',
    required: false,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },
  {
    type: 'bathroom',
    xyMin: 6,
    xyMax: 8,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.BATHROOM,
    canBeUsed: 'all',
    required: false,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },

  // Торговые помещения
  {
    type: 'pantry',
    xyMin: 4,
    xyMax: 5,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: 'all',
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },
  {
    type: 'gunShop',
    xyMin: 7,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.METAL
  },
  {
    type: 'shop',
    xyMin: 12,
    xyMax: 21,
    door: 1,
    smallerRooms: 2,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.SHOP],
    required: true,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.METAL
  },

  // Жилые помещения
  {
    type: 'livingRoom',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 0,
    size: 2,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: 'all',
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },
  {
    type: 'bedroom',
    xyMin: 6,
    xyMax: 10,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: 'all',
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },
  {
    type: 'livingHouseS',
    xyMin: 10,
    xyMax: 14,
    door: 1,
    smallerRooms: 2,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },
  {
    type: 'livingHouseM',
    xyMin: 21,
    xyMax: 24,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },
  {
    type: 'livingHouseL',
    xyMin: 24,
    xyMax: 27,
    door: 2,
    smallerRooms: 4,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },

  // Военные объекты
  {
    type: 'armory',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER, LocationSubtype.POLICE],
    required: false,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.METAL
  },
  {
    type: 'barracks',
    xyMin: 18,
    xyMax: 24,
    door: 2,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER],
    required: true,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.METAL
  },
  {
    type: 'guardPost',
    xyMin: 6,
    xyMax: 8,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER, LocationSubtype.POLICE],
    required: false,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.METAL
  },

  // Промышленные объекты
  {
    type: 'workshop',
    xyMin: 10,
    xyMax: 14,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.FACTORY,
    canBeUsed: [LocationSubtype.FACTORY, LocationSubtype.MILITARY, LocationSubtype.CAMP],
    required: false,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.METAL
  },
  {
    type: 'laboratory',
    xyMin: 18,
    xyMax: 24,
    door: 1,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.LABORATORY,
    canBeUsed: [LocationSubtype.LABORATORY, LocationSubtype.FACTORY, LocationSubtype.BUNKER],
    required: true,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },
  {
    type: 'storage',
    xyMin: 6,
    xyMax: 10,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.FACTORY,
    canBeUsed: 'all',
    required: false,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.METAL
  },

  // Сельскохозяйственные объекты
  {
    type: 'barn',
    xyMin: 18,
    xyMax: 27,
    door: 2,
    smallerRooms: 1,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.FARM,
    canBeUsed: [LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: true,
    floorMaterial: TerrainType.GROUND,
    roofMaterial: MaterialTexture.WOOD
  },
  {
    type: 'greenhouse',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 0,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.FARM,
    canBeUsed: [LocationSubtype.FARM, LocationSubtype.VILLAGE],
    required: false,
    floorMaterial: TerrainType.GROUND,
    roofMaterial: MaterialTexture.WOOD
  },

  // Общественные здания
  {
    type: 'schoolBig',
    xyMin: 21,
    xyMax: 30,
    door: 2,
    smallerRooms: 4,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SCHOOL,
    canBeUsed: [LocationSubtype.SCHOOL],
    required: true,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },
    {
    type: 'school',
    xyMin: 11,
    xyMax: 13,
    door: 2,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SCHOOL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.SCHOOL],
    required: true,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },
  {
    type: 'hotel',
    xyMin: 18,
    xyMax: 27,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.HOTEL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.HOTEL],
    required: true,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },
  {
    type: 'bar',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.BAR,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.CAMP],
    required: false,
    floorMaterial: TerrainType.WOOD,
    roofMaterial: MaterialTexture.WOOD
  },

  // Заправочные станции
  {
    type: 'gasStation',
    xyMin: 15,
    xyMax: 21,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.GASSTATION,
    canBeUsed: [LocationSubtype.GASSTATION],
    required: true,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.METAL
  },

  // Полицейские участки
  {
    type: 'policeStation',
    xyMin: 18,
    xyMax: 24,
    door: 2,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.POLICE,
    canBeUsed: [LocationSubtype.POLICE, LocationSubtype.TOWN],
    required: true,
    floorMaterial: TerrainType.TILES,
    roofMaterial: MaterialTexture.BETON
  },

  // Метро
  {
    type: 'subwayStation',
    xyMin: 24,
    xyMax: 33,
    door: 3,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SUBWAY,
    canBeUsed: [LocationSubtype.SUBWAY],
    required: true,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.BETON
  },

  // Бункеры
  {
    type: 'bunkerRoom',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.BUNKER,
    canBeUsed: [LocationSubtype.BUNKER],
    required: false,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.BETON
  },
  {
    type: 'bunkerComplex',
    xyMin: 21,
    xyMax: 30,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.BUNKER,
    canBeUsed: [LocationSubtype.BUNKER],
    required: true,
    floorMaterial: TerrainType.BETON,
    roofMaterial: MaterialTexture.BETON
  }
];
