import { DecorationZoneType, LocationSubtype } from '../../../shared/enums';

export interface HouseRoomSettings {
  type: string;
  xyMin: number;
  xyMax: number;
  door: number;
  smallerRooms: number;
  size: 1 | 2 | 3; // 1 - маленький, 2 - средний, 3 - большой
  room: boolean; // является ли комнатой (может быть внутри здания)
  building: boolean; // является ли зданием (может быть отдельно стоящим)
  decorationZoneType: DecorationZoneType;
  canBeUsed: LocationSubtype[] | 'all'; // типы локаций где может использоваться
}

export const houseRoomSettings: HouseRoomSettings[] = [
  // Медицинские помещения
  {
    type: 'firstAid',
    xyMin: 7,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.HOSPITAL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.LABORATORY, LocationSubtype.MILITARY, LocationSubtype.FACTORY]
  },
  {
    type: 'hospital',
    xyMin: 15,
    xyMax: 20,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.HOSPITAL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.HOSPITAL]
  },

  // Санитарные помещения
  {
    type: 'toilet',
    xyMin: 4,
    xyMax: 6,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.BATHROOM,
    canBeUsed: 'all'
  },
  {
    type: 'bathroom',
    xyMin: 6,
    xyMax: 8,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.BATHROOM,
    canBeUsed: 'all'
  },

  // Торговые помещения
  {
    type: 'pantry',
    xyMin: 4,
    xyMax: 5,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: 'all'
  },
  {
    type: 'gunShop',
    xyMin: 7,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE]
  },
  {
    type: 'shop',
    xyMin: 8,
    xyMax: 14,
    door: 1,
    smallerRooms: 2,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SHOP,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.SHOP]
  },

  // Жилые помещения
  {
    type: 'livingRoom',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 0,
    size: 2,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: 'all'
  },
  {
    type: 'bedroom',
    xyMin: 6,
    xyMax: 10,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: false,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: 'all'
  },
  {
    type: 'livingHouseS',
    xyMin: 10,
    xyMax: 14,
    door: 1,
    smallerRooms: 2,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE]
  },
  {
    type: 'livingHouseM',
    xyMin: 14,
    xyMax: 16,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE]
  },
  {
    type: 'livingHouseL',
    xyMin: 16,
    xyMax: 18,
    door: 2,
    smallerRooms: 4,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.VILLAGE,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.FARM, LocationSubtype.VILLAGE]
  },

  // Военные объекты
  {
    type: 'armory',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER, LocationSubtype.POLICE]
  },
  {
    type: 'barracks',
    xyMin: 12,
    xyMax: 16,
    door: 2,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER]
  },
  {
    type: 'guardPost',
    xyMin: 6,
    xyMax: 8,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.MILITARY,
    canBeUsed: [LocationSubtype.MILITARY, LocationSubtype.BUNKER, LocationSubtype.POLICE]
  },

  // Промышленные объекты
  {
    type: 'workshop',
    xyMin: 10,
    xyMax: 14,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.FACTORY,
    canBeUsed: [LocationSubtype.FACTORY, LocationSubtype.MILITARY, LocationSubtype.CAMP]
  },
  {
    type: 'laboratory',
    xyMin: 12,
    xyMax: 16,
    door: 1,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.LABORATORY,
    canBeUsed: [LocationSubtype.LABORATORY, LocationSubtype.FACTORY, LocationSubtype.BUNKER]
  },
  {
    type: 'storage',
    xyMin: 6,
    xyMax: 10,
    door: 1,
    smallerRooms: 0,
    size: 1,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.FACTORY,
    canBeUsed: 'all'
  },

  // Сельскохозяйственные объекты
  {
    type: 'barn',
    xyMin: 12,
    xyMax: 18,
    door: 2,
    smallerRooms: 1,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.FARM,
    canBeUsed: [LocationSubtype.FARM, LocationSubtype.VILLAGE]
  },
  {
    type: 'greenhouse',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 0,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.FARM,
    canBeUsed: [LocationSubtype.FARM, LocationSubtype.VILLAGE]
  },

  // Общественные здания
  {
    type: 'school',
    xyMin: 14,
    xyMax: 20,
    door: 2,
    smallerRooms: 4,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SCHOOL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.SCHOOL]
  },
  {
    type: 'hotel',
    xyMin: 12,
    xyMax: 18,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.HOTEL,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.HOTEL]
  },
  {
    type: 'bar',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.BAR,
    canBeUsed: [LocationSubtype.TOWN, LocationSubtype.VILLAGE, LocationSubtype.CAMP]
  },

  // Заправочные станции
  {
    type: 'gasStation',
    xyMin: 10,
    xyMax: 14,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.GASSTATION,
    canBeUsed: [LocationSubtype.GASSTATION]
  },

  // Полицейские участки
  {
    type: 'policeStation',
    xyMin: 12,
    xyMax: 16,
    door: 2,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.POLICE,
    canBeUsed: [LocationSubtype.POLICE, LocationSubtype.TOWN]
  },

  // Метро
  {
    type: 'subwayStation',
    xyMin: 16,
    xyMax: 22,
    door: 3,
    smallerRooms: 2,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.SUBWAY,
    canBeUsed: [LocationSubtype.SUBWAY]
  },

  // Бункеры
  {
    type: 'bunkerRoom',
    xyMin: 8,
    xyMax: 12,
    door: 1,
    smallerRooms: 1,
    size: 2,
    room: true,
    building: true,
    decorationZoneType: DecorationZoneType.BUNKER,
    canBeUsed: [LocationSubtype.BUNKER]
  },
  {
    type: 'bunkerComplex',
    xyMin: 14,
    xyMax: 20,
    door: 2,
    smallerRooms: 3,
    size: 3,
    room: false,
    building: true,
    decorationZoneType: DecorationZoneType.BUNKER,
    canBeUsed: [LocationSubtype.BUNKER]
  }
];
