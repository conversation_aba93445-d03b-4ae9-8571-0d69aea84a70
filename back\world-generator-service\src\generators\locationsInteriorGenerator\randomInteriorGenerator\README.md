# Система генерации зданий (Random Interior Generator)

Универсальная система генерации зданий для разных типов локаций в 2D изометрической игре.

## Обзор

Система автоматически генерирует здания и интерьеры на основе типа локации (`locationType` и `locationSubtype`). Поддерживает различные типы зданий от простых комнат до сложных многокомнатных структур.

## Основные компоненты

### 1. Конфигурация зданий (`houseRoomSettings.ts`)

Содержит массив `houseRoomSettings` с описанием всех доступных типов зданий и комнат:

```typescript
interface HouseRoomSettings {
  type: string;                    // Тип здания/комнаты
  xyMin: number;                   // Минимальный размер
  xyMax: number;                   // Максимальный размер
  door: number;                    // Количество дверей
  smallerRooms: number;            // Количество вложенных комнат
  size: 1 | 2 | 3;               // Размер (1-маленький, 2-средний, 3-большой)
  room: boolean;                   // Может быть комнатой внутри здания
  building: boolean;               // Может быть отдельным зданием
  decorationZoneType: DecorationZoneType; // Тип зоны декораций
  canBeUsed: LocationSubtype[] | 'all';   // Где может использоваться
  required: boolean;               // Обязательное здание для данного типа локации
  floorMaterial: TerrainType;      // Материал пола (плитка, дерево, бетон)
  roofMaterial: MaterialTexture;   // Материал крыши (дерево, металл, бетон)
}
```

### 2. Основная функция генерации (`mainInteriorGenerator.ts`)

#### `generateRundimInterior(cell, config, rng)`

Главная функция, которая:
- Определяет центр карты и размеры локации
- Фильтрует подходящие здания по `canBeUsed`
- Получает обязательные здания (`required: true`)
- Размещает маркеры зданий с отступами 3-5 тайлов от центра
- Гарантирует размещение обязательных зданий в первую очередь
- Создает здания без пересечений

### 3. Создание зданий (`buildingCreator.ts`)

#### `createBuilding(location, buildingConfig, position, rng)`

Создает отдельное здание:
- Очищает существующие декорации в области здания
- Устанавливает материалы пола и крыши согласно конфигурации
- Строит базовую геометрию (стены, двери, окна)
- Для больших зданий (size: 3) размещает вложенные комнаты по углам
- Комнаты наслаиваются поверх основного здания
- Устанавливает зоны декораций

## Типы зданий

### По размеру:
- **Size 1** (маленькие): туалет, спальня, кладовая (4-10 клеток)
- **Size 2** (средние): магазин, мастерская, жилой дом (7-16 клеток)  
- **Size 3** (большие): больница, школа, казармы (12-22 клетки)

### По назначению:
- **Жилые**: livingRoom, bedroom, livingHouseS/M/L
- **Медицинские**: firstAid, hospital
- **Торговые**: shop, gunShop, pantry
- **Военные**: armory, barracks, guardPost
- **Промышленные**: workshop, laboratory, storage
- **Сельскохозяйственные**: barn, greenhouse
- **Общественные**: school, hotel, bar
- **Специальные**: gasStation, policeStation, subwayStation

## Материалы пола и крыши

Каждое здание/комната автоматически получает подходящие материалы:

### Материалы пола (TerrainType):
- **TILES** - плитка (больницы, туалеты, школы, полиция)
- **WOOD** - дерево (жилые дома, магазины, бары, фермы)
- **BETON** - бетон (военные, промышленные, заправки, метро, бункеры)

### Материалы крыши (MaterialTexture):
- **WOOD** - дерево (жилые дома, фермы, бары)
- **METAL** - металл (военные, промышленные, магазины, заправки)
- **BETON** - бетон (больницы, школы, полиция, метро, бункеры)

## Система вложенности

Большие здания (size: 3) автоматически получают внутренние комнаты по углам:

```
Большое здание (20x20)
├── Основное помещение
├── Угол 1: toilet (4x4) - плитка/бетон
├── Угол 2: storage (6x6) - бетон/металл
├── Угол 3: bedroom (5x5) - дерево/дерево
└── Угол 4: pantry (4x4) - дерево/дерево
```

Комнаты размещаются по углам здания и наслаиваются поверх основной структуры.

## Система коллизий

- **Минимальное расстояние между зданиями**: 4 тайла
- **Отступ от краев карты**: 3 тайла
- **Минимальное расстояние от центра**: 5 тайлов
- **Максимальное расстояние от центра**: 1/2.5 от размера карты

## Зоны декораций

Каждое здание автоматически получает зону декораций (`DecorationZoneType`):

- `HOSPITAL` - медицинское оборудование
- `SHOP` - торговые прилавки, товары
- `VILLAGE` - бытовая мебель
- `MILITARY` - военное снаряжение
- `FACTORY` - промышленное оборудование
- `FARM` - сельскохозяйственные инструменты
- И другие...

## Обязательные здания

Некоторые здания помечены как обязательные (`required: true`) и гарантированно размещаются в соответствующих локациях:

- **hospital** - обязательно в локациях типа HOSPITAL
- **school** - обязательно в локациях типа SCHOOL
- **policeStation** - обязательно в локациях типа POLICE
- **gasStation** - обязательно в локациях типа GASSTATION
- **shop** - обязательно в локациях типа SHOP
- **barracks** - обязательно в военных локациях
- **barn** - обязательно на фермах
- И другие...

Система автоматически увеличивает количество зданий, если обязательных больше чем планируемое количество.

## Фильтрация по типам локаций

Здания размещаются только в подходящих локациях:

```typescript
// Пример: оружейный магазин
{
  type: 'gunShop',
  canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.VILLAGE],
  required: false
}

// Пример: обязательный магазин
{
  type: 'shop',
  canBeUsed: [LocationSubtype.SHOP],
  required: true
}
```

## Использование

Система автоматически вызывается в процессе генерации локаций:

```typescript
// В locationInteriorGenerator.ts
await generateGridForLocation(cell, config, rng);
await generateRundimInterior(cell, config, rng);  // <- Наша система
await placePresetsForLocation(cell, config, rng, legend);
```

## Настройка

Для добавления новых типов зданий:

1. Добавьте новый объект в `houseRoomSettings`
2. Определите подходящий `DecorationZoneType`
3. Укажите в каких локациях может использоваться (`canBeUsed`)

## Очистка декораций

Перед размещением здания система автоматически очищает область от существующих декораций:
- Удаляет все типы декораций в области здания
- Очищает зоны декораций в этой области
- Предотвращает конфликты между старыми и новыми декорациями

## Результат

Система создает:
- **Стены** (`LocationDecorations.WALL`) - наслаиваются для комнат
- **Двери** (`LocationDecorations.DOOR`) - автоматически размещаются
- **Окна** (`LocationDecorations.WINDOW`) - добавляются в большие комнаты
- **Пол** (`location.floor`) - по типам материалов (Record<TerrainType, Point[]>)
- **Крыша** (`location.roof`) - по типам материалов (Record<MaterialTexture, Point[]>)
- **Зоны декораций** (`DecorationZoneType`) - для правильного наполнения

Все элементы автоматически интегрируются с существующей системой декораций и обработки сторон.
