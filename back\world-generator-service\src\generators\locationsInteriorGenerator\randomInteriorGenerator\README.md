# Система генерации зданий (Random Interior Generator)

Универсальная система генерации зданий для разных типов локаций в 2D изометрической игре.

## Обзор

Система автоматически генерирует здания и интерьеры на основе типа локации (`locationType` и `locationSubtype`). Поддерживает различные типы зданий от простых комнат до сложных многокомнатных структур.

## Основные компоненты

### 1. Конфигурация зданий (`houseRoomSettings.ts`)

Содержит массив `houseRoomSettings` с описанием всех доступных типов зданий и комнат:

```typescript
interface HouseRoomSettings {
  type: string;                    // Тип здания/комнаты
  xyMin: number;                   // Минимальный размер
  xyMax: number;                   // Максимальный размер  
  door: number;                    // Количество дверей
  smallerRooms: number;            // Количество вложенных комнат
  size: 1 | 2 | 3;               // Размер (1-маленький, 2-средний, 3-большой)
  room: boolean;                   // Может быть комнатой внутри здания
  building: boolean;               // Может быть отдельным зданием
  decorationZoneType: DecorationZoneType; // Тип зоны декораций
  canBeUsed: LocationSubtype[] | 'all';   // Где может использоваться
}
```

### 2. Основная функция генерации (`mainInteriorGenerator.ts`)

#### `generateRundimInterior(cell, config, rng)`

Главная функция, которая:
- Определяет центр карты и размеры локации
- Фильтрует подходящие здания по `canBeUsed`
- Размещает маркеры зданий с отступами 3-5 тайлов от центра
- Создает здания без пересечений

### 3. Создание зданий (`buildingCreator.ts`)

#### `createBuilding(location, buildingConfig, position, rng)`

Создает отдельное здание:
- Строит базовую геометрию (стены, двери, окна)
- Для больших зданий (size: 3) размещает вложенные комнаты
- Устанавливает зоны декораций

## Типы зданий

### По размеру:
- **Size 1** (маленькие): туалет, спальня, кладовая (4-10 клеток)
- **Size 2** (средние): магазин, мастерская, жилой дом (7-16 клеток)  
- **Size 3** (большие): больница, школа, казармы (12-22 клетки)

### По назначению:
- **Жилые**: livingRoom, bedroom, livingHouseS/M/L
- **Медицинские**: firstAid, hospital
- **Торговые**: shop, gunShop, pantry
- **Военные**: armory, barracks, guardPost
- **Промышленные**: workshop, laboratory, storage
- **Сельскохозяйственные**: barn, greenhouse
- **Общественные**: school, hotel, bar
- **Специальные**: gasStation, policeStation, subwayStation

## Система вложенности

Большие здания (size: 3) автоматически получают внутренние комнаты:

```
Большое здание (16x16)
├── Основное помещение
├── Комната 1 (toilet, 4x4)
├── Комната 2 (storage, 6x6)  
└── Комната 3 (bedroom, 8x8)
```

## Система коллизий

- **Минимальное расстояние между зданиями**: 4 тайла
- **Отступ от краев карты**: 3 тайла
- **Минимальное расстояние от центра**: 5 тайлов
- **Максимальное расстояние от центра**: 1/2.5 от размера карты

## Зоны декораций

Каждое здание автоматически получает зону декораций (`DecorationZoneType`):

- `HOSPITAL` - медицинское оборудование
- `SHOP` - торговые прилавки, товары
- `VILLAGE` - бытовая мебель
- `MILITARY` - военное снаряжение
- `FACTORY` - промышленное оборудование
- `FARM` - сельскохозяйственные инструменты
- И другие...

## Фильтрация по типам локаций

Здания размещаются только в подходящих локациях:

```typescript
// Пример: оружейный магазин
{
  type: 'gunShop',
  canBeUsed: [LocationSubtype.TOWN, LocationSubtype.CAMP, LocationSubtype.VILLAGE]
}

// Пример: универсальная комната
{
  type: 'toilet', 
  canBeUsed: 'all'
}
```

## Использование

Система автоматически вызывается в процессе генерации локаций:

```typescript
// В locationInteriorGenerator.ts
await generateGridForLocation(cell, config, rng);
await generateRundimInterior(cell, config, rng);  // <- Наша система
await placePresetsForLocation(cell, config, rng, legend);
```

## Настройка

Для добавления новых типов зданий:

1. Добавьте новый объект в `houseRoomSettings`
2. Определите подходящий `DecorationZoneType`
3. Укажите в каких локациях может использоваться (`canBeUsed`)

## Результат

Система создает:
- Стены (`LocationDecorations.WALL`)
- Двери (`LocationDecorations.DOOR`) 
- Окна (`LocationDecorations.WINDOW`)
- Зоны декораций (`DecorationZoneType`)

Все элементы автоматически интегрируются с существующей системой декораций и обработки сторон.
