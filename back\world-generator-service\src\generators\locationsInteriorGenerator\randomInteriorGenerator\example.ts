/**
 * Пример использования системы генерации зданий
 * 
 * Этот файл демонстрирует, как система автоматически генерирует
 * различные типы зданий для разных типов локаций.
 */

import { houseRoomSettings } from '../constants/houseRoomSettings';
import { LocationSubtype } from '../../../shared/enums';

// Пример 1: Получение зданий для города
function getBuildingsForTown() {
  console.log('=== Здания для города (TOWN) ===');
  
  const townBuildings = houseRoomSettings.filter(building => {
    if (!building.building) return false;
    if (building.canBeUsed === 'all') return true;
    return Array.isArray(building.canBeUsed) && building.canBeUsed.includes(LocationSubtype.TOWN);
  });
  
  townBuildings.forEach(building => {
    console.log(`- ${building.type} (размер: ${building.size}, комнат: ${building.smallerRooms})`);
  });
  
  return townBuildings;
}

// Пример 2: Получение зданий для военной базы
function getBuildingsForMilitary() {
  console.log('\n=== Здания для военной базы (MILITARY) ===');
  
  const militaryBuildings = houseRoomSettings.filter(building => {
    if (!building.building) return false;
    if (building.canBeUsed === 'all') return true;
    return Array.isArray(building.canBeUsed) && building.canBeUsed.includes(LocationSubtype.MILITARY);
  });
  
  militaryBuildings.forEach(building => {
    console.log(`- ${building.type} (размер: ${building.size}, зона: ${building.decorationZoneType})`);
  });
  
  return militaryBuildings;
}

// Пример 3: Получение универсальных комнат
function getUniversalRooms() {
  console.log('\n=== Универсальные комнаты (для всех локаций) ===');
  
  const universalRooms = houseRoomSettings.filter(building => 
    building.canBeUsed === 'all'
  );
  
  universalRooms.forEach(room => {
    console.log(`- ${room.type} (${room.room ? 'комната' : 'здание'}, размер: ${room.xyMin}-${room.xyMax})`);
  });
  
  return universalRooms;
}

// Пример 4: Анализ системы размеров
function analyzeBuildingSizes() {
  console.log('\n=== Анализ размеров зданий ===');
  
  const sizeGroups = {
    small: houseRoomSettings.filter(b => b.size === 1),
    medium: houseRoomSettings.filter(b => b.size === 2), 
    large: houseRoomSettings.filter(b => b.size === 3)
  };
  
  console.log(`Маленькие (size 1): ${sizeGroups.small.length} типов`);
  console.log(`Средние (size 2): ${sizeGroups.medium.length} типов`);
  console.log(`Большие (size 3): ${sizeGroups.large.length} типов`);
  
  console.log('\nБольшие здания с вложенными комнатами:');
  sizeGroups.large
    .filter(b => b.smallerRooms > 0)
    .forEach(building => {
      console.log(`- ${building.type}: ${building.smallerRooms} комнат`);
    });
}

// Пример 5: Демонстрация процесса генерации
function demonstrateGenerationProcess() {
  console.log('\n=== Процесс генерации для деревни ===');
  
  // 1. Фильтрация зданий
  const availableBuildings = houseRoomSettings.filter(building => {
    if (!building.building) return false;
    if (building.canBeUsed === 'all') return true;
    return Array.isArray(building.canBeUsed) && building.canBeUsed.includes(LocationSubtype.VILLAGE);
  });
  
  console.log(`1. Доступно зданий: ${availableBuildings.length}`);
  
  // 2. Симуляция размещения
  const buildingCount = 4; // Например, для деревни 2-6 зданий
  console.log(`2. Планируется разместить: ${buildingCount} зданий`);
  
  // 3. Выбор случайных зданий
  const selectedBuildings = [];
  for (let i = 0; i < buildingCount; i++) {
    const randomBuilding = availableBuildings[Math.floor(Math.random() * availableBuildings.length)];
    selectedBuildings.push(randomBuilding);
  }
  
  console.log('3. Выбранные здания:');
  selectedBuildings.forEach((building, index) => {
    const size = Math.floor(Math.random() * (building.xyMax - building.xyMin + 1)) + building.xyMin;
    console.log(`   ${index + 1}. ${building.type} (${size}x${size}, зона: ${building.decorationZoneType})`);
    
    if (building.smallerRooms > 0) {
      console.log(`      └─ Внутренние комнаты: ${building.smallerRooms}`);
    }
  });
}

// Запуск примеров
export function runExamples() {
  console.log('🏗️  Примеры использования системы генерации зданий\n');
  
  getBuildingsForTown();
  getBuildingsForMilitary();
  getUniversalRooms();
  analyzeBuildingSizes();
  demonstrateGenerationProcess();
  
  console.log('\n✅ Примеры завершены!');
}

// Если файл запускается напрямую
if (require.main === module) {
  runExamples();
}
