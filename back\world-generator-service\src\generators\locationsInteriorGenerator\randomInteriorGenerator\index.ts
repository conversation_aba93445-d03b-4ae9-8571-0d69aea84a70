/**
 * Экспорты системы генерации зданий
 */

// Основная функция генерации
export { generateRundimInterior } from './mainInteriorGenerator';

// Функция создания зданий
export { createBuilding } from './buildingCreator';

// Конфигурация зданий
export { houseRoomSettings, type HouseRoomSettings } from '../constants/houseRoomSettings';

// Примеры и тесты
export { runExamples } from './example';
export { testBuildingGeneration } from './test';
