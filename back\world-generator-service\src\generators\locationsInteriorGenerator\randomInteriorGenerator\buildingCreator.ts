import { TransferLocation, Point } from "../../../shared/types/Location";
import { HouseRoomSettings, houseRoomSettings } from "../constants/houseRoomSettings";
import { LocationDecorations, DecorationZoneType } from "../../../shared/enums";

/**
 * Создает здание на указанной позиции
 */
export async function createBuilding(
  location: TransferLocation,
  buildingConfig: HouseRoomSettings,
  position: Point,
  rng: () => number
): Promise<void> {
  const [startX, startY] = position;
  const buildingSize = Math.floor(rng() * (buildingConfig.xyMax - buildingConfig.xyMin + 1)) + buildingConfig.xyMin;
  
  // Создаем базовую геометрию здания
  createBuildingWalls(location, startX, startY, buildingSize);
  
  // Добавляем двери
  addBuildingDoors(location, startX, startY, buildingSize, buildingConfig.door, rng);
  
  // Добавляем окна
  addBuildingWindows(location, startX, startY, buildingSize, rng);
  
  // Если здание большое (size: 3), размещаем внутри меньшие комнаты
  if (buildingConfig.size === 3 && buildingConfig.smallerRooms > 0) {
    await placeSmallerRooms(location, startX, startY, buildingSize, buildingConfig.smallerRooms, rng);
  }
  
  // Устанавливаем зону декораций для здания
  setDecorationZone(location, startX, startY, buildingSize, buildingConfig);
}

/**
 * Создает стены здания
 */
function createBuildingWalls(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number
): void {
  if (!location.decorations) {
    location.decorations = {};
  }
  
  if (!location.decorations[LocationDecorations.WALL]) {
    location.decorations[LocationDecorations.WALL] = [];
  }
  
  const walls = location.decorations[LocationDecorations.WALL];
  
  // Верхняя и нижняя стены
  for (let x = startX; x < startX + size; x++) {
    walls.push([x, startY]); // Верхняя стена
    walls.push([x, startY + size - 1]); // Нижняя стена
  }
  
  // Левая и правая стены
  for (let y = startY + 1; y < startY + size - 1; y++) {
    walls.push([startX, y]); // Левая стена
    walls.push([startX + size - 1, y]); // Правая стена
  }
}

/**
 * Добавляет двери в здание
 */
function addBuildingDoors(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  doorCount: number,
  rng: () => number
): void {
  if (!location.decorations) {
    location.decorations = {};
  }
  
  if (!location.decorations[LocationDecorations.DOOR]) {
    location.decorations[LocationDecorations.DOOR] = [];
  }
  
  const doors = location.decorations[LocationDecorations.DOOR];
  const walls = location.decorations[LocationDecorations.WALL] || [];
  
  // Возможные позиции для дверей (середины стен)
  const possibleDoorPositions: Point[] = [
    [startX + Math.floor(size / 2), startY], // Верхняя стена
    [startX + Math.floor(size / 2), startY + size - 1], // Нижняя стена
    [startX, startY + Math.floor(size / 2)], // Левая стена
    [startX + size - 1, startY + Math.floor(size / 2)] // Правая стена
  ];
  
  // Размещаем двери
  for (let i = 0; i < Math.min(doorCount, possibleDoorPositions.length); i++) {
    const doorIndex = Math.floor(rng() * possibleDoorPositions.length);
    const doorPosition = possibleDoorPositions.splice(doorIndex, 1)[0];
    
    doors.push(doorPosition);
    
    // Удаляем стену в позиции двери
    const wallIndex = walls.findIndex(wall => wall[0] === doorPosition[0] && wall[1] === doorPosition[1]);
    if (wallIndex !== -1) {
      walls.splice(wallIndex, 1);
    }
  }
}

/**
 * Добавляет окна в здание
 */
function addBuildingWindows(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  rng: () => number
): void {
  if (!location.decorations) {
    location.decorations = {};
  }
  
  if (!location.decorations[LocationDecorations.WINDOW]) {
    location.decorations[LocationDecorations.WINDOW] = [];
  }
  
  const windows = location.decorations[LocationDecorations.WINDOW];
  const walls = location.decorations[LocationDecorations.WALL] || [];
  const doors = location.decorations[LocationDecorations.DOOR] || [];
  
  // Определяем количество окон (1-3 в зависимости от размера здания)
  const windowCount = Math.min(3, Math.floor(size / 4) + 1);
  
  // Собираем все позиции стен, исключая углы и двери
  const availableWallPositions: Point[] = [];
  
  for (const wall of walls) {
    const [x, y] = wall;
    
    // Проверяем, что это не угол
    const isCorner = (x === startX || x === startX + size - 1) && 
                     (y === startY || y === startY + size - 1);
    
    // Проверяем, что рядом нет двери
    const nearDoor = doors.some(door => 
      Math.abs(door[0] - x) <= 1 && Math.abs(door[1] - y) <= 1
    );
    
    if (!isCorner && !nearDoor) {
      availableWallPositions.push(wall);
    }
  }
  
  // Размещаем окна
  for (let i = 0; i < Math.min(windowCount, availableWallPositions.length); i++) {
    const windowIndex = Math.floor(rng() * availableWallPositions.length);
    const windowPosition = availableWallPositions.splice(windowIndex, 1)[0];
    
    windows.push(windowPosition);
    
    // Удаляем стену в позиции окна
    const wallIndex = walls.findIndex(wall => wall[0] === windowPosition[0] && wall[1] === windowPosition[1]);
    if (wallIndex !== -1) {
      walls.splice(wallIndex, 1);
    }
  }
}

/**
 * Размещает меньшие комнаты внутри большого здания
 */
async function placeSmallerRooms(
  location: TransferLocation,
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number,
  roomCount: number,
  rng: () => number
): Promise<void> {
  // Внутренняя область здания (исключая стены)
  const innerStartX = buildingStartX + 1;
  const innerStartY = buildingStartY + 1;
  const innerSize = buildingSize - 2;

  if (innerSize < 6) return; // Слишком маленькое здание для комнат

  const occupiedAreas: { x: number; y: number; width: number; height: number }[] = [];

  // Получаем подходящие типы комнат (размер 1 и 2)
  const availableRoomTypes = getSmallerRoomTypes();

  for (let i = 0; i < roomCount; i++) {
    // Выбираем случайный тип комнаты
    const roomType = availableRoomTypes[Math.floor(rng() * availableRoomTypes.length)];

    // Определяем размер комнаты на основе конфигурации
    const roomSize = Math.floor(rng() * (roomType.xyMax - roomType.xyMin + 1)) + roomType.xyMin;

    // Проверяем, помещается ли комната
    if (roomSize > innerSize - 2) continue;

    // Пытаемся найти место для комнаты
    let placed = false;
    for (let attempt = 0; attempt < 30 && !placed; attempt++) {
      const roomX = innerStartX + Math.floor(rng() * (innerSize - roomSize));
      const roomY = innerStartY + Math.floor(rng() * (innerSize - roomSize));

      // Проверяем коллизии с отступом между комнатами
      const newArea = {
        x: roomX - 1,
        y: roomY - 1,
        width: roomSize + 2,
        height: roomSize + 2
      };

      if (!checkAreaCollision(newArea, occupiedAreas)) {
        // Создаем комнату с учетом её типа
        createTypedRoom(location, roomX, roomY, roomSize, roomType, rng);
        occupiedAreas.push({ x: roomX, y: roomY, width: roomSize, height: roomSize });
        placed = true;
      }
    }
  }
}

/**
 * Получает типы комнат, подходящие для размещения внутри зданий
 */
function getSmallerRoomTypes(): HouseRoomSettings[] {
  return houseRoomSettings.filter(room =>
    room.room && (room.size === 1 || room.size === 2) && room.canBeUsed === 'all'
  );
}

/**
 * Создает комнату определенного типа
 */
function createTypedRoom(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  roomType: HouseRoomSettings,
  rng: () => number
): void {
  // Создаем стены комнаты
  createBuildingWalls(location, startX, startY, size);

  // Добавляем дверь в комнату
  addBuildingDoors(location, startX, startY, size, 1, rng);

  // Устанавливаем зону декораций для комнаты
  setDecorationZone(location, startX, startY, size, roomType);
}



/**
 * Устанавливает зону декораций для здания
 */
function setDecorationZone(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  buildingConfig: HouseRoomSettings
): void {
  if (!location.decorationZoneType) {
    location.decorationZoneType = {};
  }
  
  if (!location.decorationZoneType[buildingConfig.decorationZoneType]) {
    location.decorationZoneType[buildingConfig.decorationZoneType] = [];
  }
  
  const zoneId = Math.floor(Math.random() * 1000000);
  
  // Добавляем все клетки здания в зону декораций
  for (let x = startX; x < startX + size; x++) {
    for (let y = startY; y < startY + size; y++) {
      location.decorationZoneType[buildingConfig.decorationZoneType].push([x, y, zoneId]);
    }
  }
}

/**
 * Проверяет коллизию между областями
 */
function checkAreaCollision(
  newArea: { x: number; y: number; width: number; height: number },
  existingAreas: { x: number; y: number; width: number; height: number }[]
): boolean {
  for (const area of existingAreas) {
    if (
      newArea.x < area.x + area.width &&
      newArea.x + newArea.width > area.x &&
      newArea.y < area.y + area.height &&
      newArea.y + newArea.height > area.y
    ) {
      return true;
    }
  }
  return false;
}
