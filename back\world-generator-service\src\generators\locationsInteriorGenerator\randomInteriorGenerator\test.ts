import { generateRundimInterior } from './mainInteriorGenerator';
import { LocationSubtype, LocationType, TerrainType, MaterialTexture } from '../../../shared/enums';
import { TransferLocation } from '../../../shared/types/Location';
import { WorldMapCell } from '../../../shared/types/World';
import { LocationConfig } from '../constants/locationConfig';

// Простой тест для проверки работы системы генерации зданий
async function testBuildingGeneration() {
  console.log('Тестирование системы генерации зданий...');
  
  // Создаем тестовую локацию
  const testLocation: TransferLocation = {
    id: 'test-location',
    name: 'Test Location',
    description: 'Test Description',
    locationSize: [50, 50],
    type: LocationType.OUTDOOR,
    subtype: LocationSubtype.TOWN,
    morality: 10,
    terrain: TerrainType.WASTELAND,
    playerPresent: false,
    playerPosition: [25, 25],
    spawnPosition: [25, 25],
    goBackPosition: [[25, 24]],
    decorations: {},
    decorationZoneType: {},
    decorationSide: [],
    textureMaterial: MaterialTexture.WOOD,
    interactive: [],
    isDiscovered: false,
    isVisible: true
  };
  
  // Создаем тестовую ячейку мира
  const testCell: WorldMapCell = {
    pos: { x: 0, y: 0 },
    blocked: false,
    terrarianMarker: 0,
    terrain: TerrainType.WASTELAND,
    height: 1,
    location: testLocation,
    decoration: undefined,
    fogOfWar: false,
    locationNear: false,
    imgDirection: 1,
    LVLZone: 1
  };
  
  // Создаем тестовую конфигурацию
  const testConfig: LocationConfig = {
    type: LocationType.OUTDOOR,
    terrain: TerrainType.WASTELAND,
    moralityRange: [1, 20],
    size: [50, 50],
    beach: false,
    buildings: { min: 3, max: 5 }
  };
  
  // Простой RNG для тестирования
  let seed = 12345;
  const rng = () => {
    seed = (seed * 9301 + 49297) % 233280;
    return seed / 233280;
  };
  
  try {
    // Запускаем генерацию
    await generateRundimInterior(testCell, testConfig, rng);
    
    console.log('✅ Генерация завершена успешно!');
    console.log('Результаты:');
    console.log('- Стены:', testLocation.decorations?.wall?.length || 0);
    console.log('- Двери:', testLocation.decorations?.door?.length || 0);
    console.log('- Окна:', testLocation.decorations?.window?.length || 0);
    console.log('- Зоны декораций:', Object.keys(testLocation.decorationZoneType || {}).length);
    
    // Выводим информацию о зонах декораций
    if (testLocation.decorationZoneType) {
      for (const [zoneType, zones] of Object.entries(testLocation.decorationZoneType)) {
        console.log(`- Зона ${zoneType}: ${zones.length} клеток`);
      }
    }
    
  } catch (error) {
    console.error('❌ Ошибка при генерации:', error);
  }
}

// Экспортируем функцию для возможного использования
export { testBuildingGeneration };

// Если файл запускается напрямую, выполняем тест
if (require.main === module) {
  testBuildingGeneration();
}
